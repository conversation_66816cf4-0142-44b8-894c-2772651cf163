/* HamburgerMenu.css - glassmorphism style for menu */
.hamburger-menu {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 250px;
  background: rgba(255,255,255,0.15);
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.37);
  backdrop-filter: blur(8px);
  border-radius: 0 20px 20px 0;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
  display: flex;
  flex-direction: column;
  padding: 2rem 1rem 1rem 1rem;
}
.hamburger-menu.open {
  transform: translateX(0);
}
.close-btn {
  align-self: flex-end;
  font-size: 2rem;
  background: none;
  border: none;
  color: #333;
  margin-bottom: 2rem;
  cursor: pointer;
}
.menu-link {
  display: block;
  margin-bottom: 1.5rem;
  text-align: left;
  width: 100%;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
