import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { createPersonnel } from '../services/personnelsService';

const AddPersonnel = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nom: '',
    tel: '',
    mail: '',
    adresse: '',
    date_naissance: '',
    user_id: 1 // This should be dynamic based on current user or selection
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    clearErrors();
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Name is required';
    } else if (formData.nom.length < 2) {
      errors.nom = 'Name must be at least 2 characters';
    } else if (formData.nom.length > 100) {
      errors.nom = 'Name must be less than 100 characters';
    }

    if (formData.tel && formData.tel.length > 20) {
      errors.tel = 'Phone number must be less than 20 characters';
    }

    if (formData.mail && formData.mail.length > 100) {
      errors.mail = 'Email must be less than 100 characters';
    } else if (formData.mail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.mail)) {
      errors.mail = 'Please enter a valid email address';
    }

    if (formData.adresse && formData.adresse.length > 255) {
      errors.adresse = 'Address must be less than 255 characters';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const clearErrors = () => {
    setError('');
    setFieldErrors({});
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setFieldErrors({});

    if (!validateForm()) {
      setLoading(false);
      return;
    }

    try {
      const result = await createPersonnel(formData);
      
      if (result.success) {
        navigate('/personnels');
      } else {
        setError(result.error || 'Failed to create personnel');
      }
    } catch (err) {
      console.error('Add personnel error:', err);
      setError('Unable to create personnel. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/personnels')}
          className="glass-button p-3 max-w-fit"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-3xl font-bold text-white">Add Personnel</h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-4xl mx-auto"
      >
        <div className="glass-card form-container">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-400 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="text-white" size={24} />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Create New Personnel</h2>
            <p className="text-white/70">Enter the personnel information below</p>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-6 p-4 bg-red-500/20 border border-red-400/50 rounded-lg"
            >
              <p className="text-red-300 text-sm">{error}</p>
            </motion.div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <input
                type="text"
                name="nom"
                placeholder="Enter full name"
                value={formData.nom}
                onChange={handleChange}
                className={`glass-input ${fieldErrors.nom ? 'error' : ''}`}
                required
              />
              {fieldErrors.nom && (
                <div className="field-error">{fieldErrors.nom}</div>
              )}
            </div>

            <div className="form-group">
              <input
                type="tel"
                name="tel"
                placeholder="Enter phone number (optional)"
                value={formData.tel}
                onChange={handleChange}
                className={`glass-input ${fieldErrors.tel ? 'error' : ''}`}
              />
              {fieldErrors.tel && (
                <div className="field-error">{fieldErrors.tel}</div>
              )}
            </div>

            <div className="form-group">
              <input
                type="email"
                name="mail"
                placeholder="Enter email address (optional)"
                value={formData.mail}
                onChange={handleChange}
                className={`glass-input ${fieldErrors.mail ? 'error' : ''}`}
              />
              {fieldErrors.mail && (
                <div className="field-error">{fieldErrors.mail}</div>
              )}
            </div>

            <div className="form-group">
              <input
                type="text"
                name="adresse"
                placeholder="Enter address (optional)"
                value={formData.adresse}
                onChange={handleChange}
                className={`glass-input ${fieldErrors.adresse ? 'error' : ''}`}
              />
              {fieldErrors.adresse && (
                <div className="field-error">{fieldErrors.adresse}</div>
              )}
            </div>

            <div className="form-group">
              <input
                type="date"
                name="date_naissance"
                placeholder="Birth date (optional)"
                value={formData.date_naissance}
                onChange={handleChange}
                className="glass-input"
              />
            </div>

            <div className="flex gap-4 mt-8">
              <button
                type="button"
                onClick={() => navigate('/personnels')}
                className="glass-button bg-white/10 hover:bg-white/20 flex-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="glass-button flex-1"
              >
                {loading ? 'Creating...' : 'Create Personnel'}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default AddPersonnel;
