import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';
import { createPersonnel } from '../services/personnelsService';

const MultiStepRegister = () => {
  const [step, setStep] = useState(1);
  const [userData, setUserData] = useState({ name: '', username: '', password: '' });
  const [personnelData, setPersonnelData] = useState({ nom: '', tel: '', mail: '', adresse: '', date_naissance: '' });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleUserChange = (e) => {
    setUserData({ ...userData, [e.target.name]: e.target.value });
  };
  const handlePersonnelChange = (e) => {
    setPersonnelData({ ...personnelData, [e.target.name]: e.target.value });
  };

  const handleNext = (e) => {
    e.preventDefault();
    setError('');
    // Basic validation for user step
    if (!userData.name || !userData.username || !userData.password) {
      setError('Veuillez remplir tous les champs requis.');
      return;
    }
    setStep(2);
  };

  const handleBack = (e) => {
    e.preventDefault();
    setError('');
    setStep(1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);
    // Register user first
    const result = await authService.register(userData);
    if (!result.success || !result.user?.id) {
      setError(result.error || 'Registration failed');
      setLoading(false);
      return;
    }
    // Register personnel
    const personnelResult = await createPersonnel({ ...personnelData, user_id: result.user.id });
    setLoading(false);
    if (personnelResult.success) {
      navigate('/login');
    } else {
      setError(personnelResult.error || 'Failed to save personnel details');
    }
  };

  return (
    <div className="glass-card form-container">
      {step === 1 && (
        <form onSubmit={handleNext}>
          <h2>Créer un compte</h2>
          <input className="glass-input" name="name" placeholder="Nom" value={userData.name} onChange={handleUserChange} required />
          <input className="glass-input" name="username" placeholder="Nom d'utilisateur" value={userData.username} onChange={handleUserChange} required />
          <input className="glass-input" name="password" type="password" placeholder="Mot de passe" value={userData.password} onChange={handleUserChange} required />
          {error && <div className="error-message">{error}</div>}
          <button className="glass-button" type="submit">Suivant</button>
        </form>
      )}
      {step === 2 && (
        <form onSubmit={handleSubmit}>
          <h2>Informations du personnel</h2>
          <input className="glass-input" name="nom" placeholder="Nom" value={personnelData.nom} onChange={handlePersonnelChange} required />
          <input className="glass-input" name="tel" placeholder="Téléphone" value={personnelData.tel} onChange={handlePersonnelChange} />
          <input className="glass-input" name="mail" placeholder="Email" value={personnelData.mail} onChange={handlePersonnelChange} />
          <input className="glass-input" name="adresse" placeholder="Adresse" value={personnelData.adresse} onChange={handlePersonnelChange} />
          <input className="glass-input" name="date_naissance" type="date" value={personnelData.date_naissance} onChange={handlePersonnelChange} />
          {error && <div className="error-message">{error}</div>}
          <div style={{ display: 'flex', gap: '1rem' }}>
            <button className="glass-button" onClick={handleBack}>Retour</button>
            <button className="glass-button" type="submit" disabled={loading}>{loading ? 'Enregistrement...' : 'Terminer'}</button>
          </div>
        </form>
      )}
    </div>
  );
};

export default MultiStepRegister;
