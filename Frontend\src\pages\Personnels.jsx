import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Plus, Edit, Trash2, Users } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getPersonnels, deletePersonnel } from '../services/personnelsService';

const Personnels = () => {
  const navigate = useNavigate();
  const [personnels, setPersonnels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    loadPersonnels();
  }, []);

  const loadPersonnels = async () => {
    try {
      setLoading(true);
      const data = await getPersonnels();
      setPersonnels(Array.isArray(data) ? data : []);
    } catch (err) {
      setError('Failed to load personnels');
      setPersonnels([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this personnel?')) {
      try {
        await deletePersonnel(id);
        await loadPersonnels(); // Reload the list
      } catch (err) {
        setError('Failed to delete personnel');
      }
    }
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/dashboard')}
          className="glass-button p-3 max-w-fit"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-3xl font-bold text-white">Personnels</h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-6xl mx-auto"
      >
        <div className="glass-card p-6">
          {/* Add Button */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-white">Manage Personnels</h2>
            <button
              onClick={() => navigate('/personnels/add')}
              className="glass-button flex items-center gap-2 max-w-fit"
            >
              <Plus size={16} />
              Add Personnel
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-6 p-4 bg-red-500/20 border border-red-400/50 rounded-lg"
            >
              <p className="text-red-300 text-sm">{error}</p>
            </motion.div>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              <p className="text-white/70 mt-2">Loading personnels...</p>
            </div>
          ) : (
            <>
              {/* Personnels List */}
              {personnels.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="mx-auto text-white/50 mb-4" size={48} />
                  <p className="text-white/70 text-lg">No personnels found</p>
                  <p className="text-white/50 text-sm mt-2">Click "Add Personnel" to create your first personnel</p>
                </div>
              ) : (
                <div className="grid gap-4">
                  {personnels.map((personnel) => (
                    <motion.div
                      key={personnel.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="glass-card p-4 hover:bg-white/15 transition-all duration-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-blue-400 rounded-lg flex items-center justify-center">
                            <Users className="text-white" size={20} />
                          </div>
                          <div>
                            <h3 className="text-white font-semibold text-lg">{personnel.nom}</h3>
                            <div className="text-white/60 text-sm space-y-1">
                              <p>ID: {personnel.id}</p>
                              {personnel.tel && <p>Tel: {personnel.tel}</p>}
                              {personnel.mail && <p>Email: {personnel.mail}</p>}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => navigate(`/personnels/edit/${personnel.id}`)}
                            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                            title="Edit"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(personnel.id)}
                            className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200"
                            title="Delete"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default Personnels;
