import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getComptes, deleteCompte } from '../services/comptesService';

const ComptesList = () => {
  const [comptes, setComptes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchComptes();
  }, []);

  const fetchComptes = async () => {
    setLoading(true);
    const data = await getComptes();
    setComptes(Array.isArray(data) ? data : []);
    setLoading(false);
  };

  const handleDelete = async (id) => {
    await deleteCompte(id);
    fetchComptes();
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
      <div className="list-header">
        <h2 className="text-2xl font-bold">Comptes</h2>
        <Link to="/comptes/add" className="glass-button">Add Compte</Link>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="entity-list">
          {comptes.map(compte => (
            <div key={compte.id} className="glass-card entity-item">
              <span>{compte.name}</span>
              <div>
                <Link to={`/comptes/edit/${compte.id}`} className="glass-button">Edit</Link>
                <button onClick={() => handleDelete(compte.id)} className="glass-button">Delete</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default ComptesList;
