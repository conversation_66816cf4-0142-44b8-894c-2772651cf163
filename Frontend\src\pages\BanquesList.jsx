import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getBanques, deleteBanque } from '../services/banquesService';

const BanquesList = () => {
  const [banques, setBanques] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBanques();
  }, []);

  const fetchBanques = async () => {
    setLoading(true);
    const data = await getBanques();
    setBanques(Array.isArray(data) ? data : []);
    setLoading(false);
  };

  const handleDelete = async (id) => {
    await deleteBanque(id);
    fetchBanques();
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
      <div className="list-header">
        <h2 className="text-2xl font-bold">Banques</h2>
        <Link to="/banques/add" className="glass-button">Add Banque</Link>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="entity-list">
          {banques.map(banque => (
            <div key={banque.id} className="glass-card entity-item">
              <span>{banque.name}</span>
              <div>
                <Link to={`/banques/edit/${banque.id}`} className="glass-button">Edit</Link>
                <button onClick={() => handleDelete(banque.id)} className="glass-button">Delete</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default BanquesList;
