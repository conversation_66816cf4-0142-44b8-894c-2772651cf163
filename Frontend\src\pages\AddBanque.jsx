import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Building2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const AddBanque = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nom: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    clearErrors();
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.nom.trim()) {
      errors.nom = 'Bank name is required';
    } else if (formData.nom.length < 2) {
      errors.nom = 'Bank name must be at least 2 characters';
    } else if (formData.nom.length > 100) {
      errors.nom = 'Bank name must be less than 100 characters';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const clearErrors = () => {
    setError('');
    setFieldErrors({});
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setFieldErrors({});

    if (!validateForm()) {
      setLoading(false);
      return;
    }

    try {
      // For now, we'll simulate the API call
      // In a real implementation, you would call the banques service
      console.log('Creating banque:', formData);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Navigate back to banques list
      navigate('/banques');
    } catch (err) {
      console.error('Add banque error:', err);
      setError('Unable to create banque. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => navigate('/banques')}
          className="glass-button p-3 max-w-fit"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-3xl font-bold text-white">Add Banque</h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-4xl mx-auto"
      >
        <div className="glass-card form-container">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Building2 className="text-white" size={24} />
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">Create New Banque</h2>
            <p className="text-white/70">Enter the bank information below</p>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mb-6 p-4 bg-red-500/20 border border-red-400/50 rounded-lg"
            >
              <p className="text-red-300 text-sm">{error}</p>
            </motion.div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <input
                type="text"
                name="nom"
                placeholder="Enter bank name"
                value={formData.nom}
                onChange={handleChange}
                className={`glass-input ${fieldErrors.nom ? 'error' : ''}`}
                required
              />
              {fieldErrors.nom && (
                <div className="field-error">{fieldErrors.nom}</div>
              )}
            </div>

            <div className="flex gap-4 mt-8">
              <button
                type="button"
                onClick={() => navigate('/banques')}
                className="glass-button bg-white/10 hover:bg-white/20 flex-1"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="glass-button flex-1"
              >
                {loading ? 'Creating...' : 'Create Banque'}
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default AddBanque;
