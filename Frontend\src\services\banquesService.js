import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getBanques = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/banques`);
    return response.data;
  } catch (error) {
    return [];
  }
};

export const deleteBanque = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/banques/${id}`);
  } catch (error) {
    // handle error
  }
};
