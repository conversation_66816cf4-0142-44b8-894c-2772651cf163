2025-07-16 19:45:11 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 19:46:00 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 19:47:28 error: [Cake\Http\Exception\InvalidCsrfTokenException] Missing or incorrect CSRF cookie type. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\Middleware\CsrfProtectionMiddleware.php on line 354
Stack Trace:
- CORE\src\Http\Middleware\CsrfProtectionMiddleware.php:165
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Middleware\BodyParserMiddleware.php:172
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-16 21:11:16 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:13:00 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:13:24 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5173/
2025-07-16 21:17:03 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:95
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
Referer URL: http://localhost:5174/
2025-07-16 21:17:20 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
Referer URL: http://localhost:5174/
2025-07-22 10:02:28 error: [Cake\Database\Exception\MissingConnectionException] Connection to Mysql could not be established: SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Database\Driver.php on line 202
Exception Attributes: array (
  'driver' => 'Mysql',
  'reason' => 'SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée',
)
Stack Trace:
- CORE\src\Database\Driver\Mysql.php:158
- CORE\src\Database\Schema\SchemaDialect.php:60
- CORE\src\Database\Driver\Mysql.php:204
- CORE\src\Database\Schema\Collection.php:97
- CORE\src\Database\Schema\Collection.php:87
- CORE\src\Database\Schema\CachedCollection.php:107
- CORE\src\ORM\Table.php:526
- CORE\src\ORM\Query\CommonQueryTrait.php:48
- CORE\src\ORM\Query\SelectQuery.php:202
- CORE\src\ORM\Query\QueryFactory.php:34
- CORE\src\ORM\Table.php:1748
- CORE\src\ORM\Table.php:1279
- APP/Controller\UsersController.php:21
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Caused by: [PDOException] SQLSTATE[HY000] [2002] Aucune connexion n’a pu être établie car l’ordinateur cible l’a expressément refusée in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Database\Driver.php on line 191
Stack Trace:
- CORE\src\Database\Driver.php:191
- CORE\src\Core\Retry\CommandRetry.php:71
- CORE\src\Database\Driver.php:200
- CORE\src\Database\Driver\Mysql.php:158
- CORE\src\Database\Schema\SchemaDialect.php:60
- CORE\src\Database\Driver\Mysql.php:204
- CORE\src\Database\Schema\Collection.php:97
- CORE\src\Database\Schema\Collection.php:87
- CORE\src\Database\Schema\CachedCollection.php:107
- CORE\src\ORM\Table.php:526
- CORE\src\ORM\Query\CommonQueryTrait.php:48
- CORE\src\ORM\Query\SelectQuery.php:202
- CORE\src\ORM\Query\QueryFactory.php:34
- CORE\src\ORM\Table.php:1748
- CORE\src\ORM\Table.php:1279
- APP/Controller\UsersController.php:21
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users
2025-07-22 10:02:56 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 10:03:08 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:100
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/login
2025-07-22 10:05:49 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 10:22:29 error: [Cake\Http\Exception\MethodNotAllowedException] Method Not Allowed in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Http\ServerRequest.php on line 1442
Stack Trace:
- APP/Controller\UsersController.php:66
- CORE\src\Controller\Controller.php:505
- CORE\src\Controller\ControllerFactory.php:166
- CORE\src\Controller\ControllerFactory.php:141
- CORE\src\Http\BaseApplication.php:362
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /users/register
2025-07-22 22:45:15 error: [Cake\Http\Exception\MissingControllerException] Controller class `Banque` could not be found. in C:\Users\<USER>\Desktop\stage\Backend\vendor\cakephp\cakephp\src\Controller\ControllerFactory.php on line 335
Exception Attributes: array (
  'controller' => 'Banque',
  'plugin' => NULL,
  'prefix' => NULL,
  '_ext' => NULL,
)
Stack Trace:
- CORE\src\Controller\ControllerFactory.php:77
- CORE\src\Http\BaseApplication.php:360
- CORE\src\Http\Runner.php:86
- CORE\src\Http\Middleware\BodyParserMiddleware.php:157
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\RoutingMiddleware.php:117
- CORE\src\Http\Runner.php:82
- CORE\src\Routing\Middleware\AssetMiddleware.php:70
- CORE\src\Http\Runner.php:82
- CORE\src\Error\Middleware\ErrorHandlerMiddleware.php:115
- CORE\src\Http\Runner.php:82
- APP/Middleware\CorsMiddleware.php:53
- CORE\src\Http\Runner.php:82
- ROOT\vendor\cakephp\debug_kit\src\Middleware\DebugKitMiddleware.php:60
- CORE\src\Http\Runner.php:82
- CORE\src\Http\Runner.php:60
- CORE\src\Http\Server.php:104
- ROOT\webroot\index.php:37
- [main]:

Request URL: /banque
