import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Banques from './pages/Banques';
import AddBanque from './pages/AddBanque';
import Personnels from './pages/Personnels';
import AddPersonnel from './pages/AddPersonnel';
import Comptes from './pages/Comptes';
import { authService } from './services/authService';

// Protected Route component
const ProtectedRoute = ({ children }) => {
  return authService.isAuthenticated() ? children : <Navigate to="/login" />;
};

// Public Route component (redirect to dashboard if already logged in)
const PublicRoute = ({ children }) => {
  return !authService.isAuthenticated() ? children : <Navigate to="/dashboard" />;
};

function App() {
  return (
    <Router>
      <Routes>
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="/users"
          element={
            <ProtectedRoute>
              <Users />
            </ProtectedRoute>
          }
        />
        <Route
          path="/banques"
          element={
            <ProtectedRoute>
              <Banques />
            </ProtectedRoute>
          }
        />
        <Route
          path="/banques/add"
          element={
            <ProtectedRoute>
              <AddBanque />
            </ProtectedRoute>
          }
        />
        <Route
          path="/personnels"
          element={
            <ProtectedRoute>
              <Personnels />
            </ProtectedRoute>
          }
        />
        <Route
          path="/personnels/add"
          element={
            <ProtectedRoute>
              <AddPersonnel />
            </ProtectedRoute>
          }
        />
        <Route
          path="/comptes"
          element={
            <ProtectedRoute>
              <Comptes />
            </ProtectedRoute>
          }
        />
        <Route path="/" element={<Navigate to="/login" />} />
      </Routes>
    </Router>
  );
}

export default App;
