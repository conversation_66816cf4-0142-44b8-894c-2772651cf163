/* AppLayout.css - layout and hamburger button styles */
.app-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(120deg, #e0eafc 0%, #cfdef3 100%);
}
.hamburger {
  position: fixed;
  top: 1.5rem;
  left: 1.5rem;
  z-index: 1100;
  background: rgba(255,255,255,0.25);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 16px 0 rgba(31,38,135,0.17);
  transition: background 0.2s;
}
.hamburger:hover {
  background: rgba(255,255,255,0.4);
}
.hamburger-icon {
  font-size: 2rem;
  color: #333;
}
.main-content {
  flex: 1;
  margin: 2rem auto;
  max-width: 900px;
  min-width: 320px;
  padding: 2rem;
  border-radius: 24px;
  box-shadow: 0 8px 32px 0 rgba(31,38,135,0.17);
  background: rgba(255,255,255,0.25);
  backdrop-filter: blur(8px);
}
@media (max-width: 600px) {
  .main-content {
    padding: 1rem;
    margin: 1rem;
  }
}
