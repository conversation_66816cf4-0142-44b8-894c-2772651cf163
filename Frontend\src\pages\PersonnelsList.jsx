import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getPersonnels, deletePersonnel } from '../services/personnelsService';

const PersonnelsList = () => {
  const [personnels, setPersonnels] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPersonnels();
  }, []);

  const fetchPersonnels = async () => {
    setLoading(true);
    const data = await getPersonnels();
    setPersonnels(Array.isArray(data) ? data : []);
    setLoading(false);
  };

  const handleDelete = async (id) => {
    await deletePersonnel(id);
    fetchPersonnels();
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
      <div className="list-header">
        <h2 className="text-2xl font-bold">Personnels</h2>
        <Link to="/personnels/add" className="glass-button">Add Personnel</Link>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="entity-list">
          {personnels.map(personnel => (
            <div key={personnel.id} className="glass-card entity-item">
              <span>{personnel.name}</span>
              <div>
                <Link to={`/personnels/edit/${personnel.id}`} className="glass-button">Edit</Link>
                <button onClick={() => handleDelete(personnel.id)} className="glass-button">Delete</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default PersonnelsList;
