import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getComptes = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/comptes`);
    return response.data;
  } catch (error) {
    return [];
  }
};

export const deleteCompte = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/comptes/${id}`);
  } catch (error) {
    // handle error
  }
};
