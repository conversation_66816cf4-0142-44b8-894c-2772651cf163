import { useState } from 'react';
import { motion } from 'framer-motion';
import { LogOut, Menu, X, Wallet, CreditCard, Users, Building2, UserCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';

const Dashboard = () => {
  const navigate = useNavigate();
  const user = authService.getCurrentUser();
  const [menuOpen, setMenuOpen] = useState(false);

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  const menuItems = [
    { name: 'Users', icon: UserCircle, path: '/users' },
    { name: 'Banques', icon: Building2, path: '/banques' },
    { name: 'Personnels', icon: Users, path: '/personnels' },
    { name: 'Comptes', icon: CreditCard, path: '/comptes' }
  ];

  return (
    <div className="min-h-screen relative">
      {/* Hamburger Menu <PERSON> */}
      <button
        onClick={() => setMenuOpen(true)}
        className="fixed top-6 left-6 z-50 glass-button p-3 max-w-fit"
      >
        <Menu size={20} />
      </button>

      {/* Hamburger Menu Overlay */}
      {menuOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setMenuOpen(false)}
        />
      )}

      {/* Hamburger Menu */}
      <motion.div
        initial={{ x: '-100%' }}
        animate={{ x: menuOpen ? 0 : '-100%' }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="fixed top-0 left-0 h-full w-80 glass-card z-50 p-6"
        style={{ borderRadius: '0 1rem 1rem 0' }}
      >
        {/* Menu Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-3">
            <Wallet className="text-white" size={24} />
            <h2 className="text-xl font-bold text-white">E-Wallet</h2>
          </div>
          <button
            onClick={() => setMenuOpen(false)}
            className="text-white/70 hover:text-white transition-colors p-2"
          >
            <X size={20} />
          </button>
        </div>

        {/* User Info */}
        <div className="mb-8 p-4 glass-card">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center">
              <UserCircle className="text-white" size={24} />
            </div>
            <div>
              <p className="text-white font-medium">{user?.name || 'User'}</p>
              <p className="text-white/60 text-sm">{user?.username || 'username'}</p>
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <nav className="space-y-2">
          {menuItems.map((item) => (
            <button
              key={item.name}
              onClick={() => {
                navigate(item.path);
                setMenuOpen(false);
              }}
              className="w-full flex items-center gap-3 p-3 text-white/80 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
            >
              <item.icon size={20} />
              <span>{item.name}</span>
            </button>
          ))}
        </nav>

        {/* Logout Button */}
        <div className="absolute bottom-6 left-6 right-6">
          <button
            onClick={handleLogout}
            className="w-full glass-button flex items-center justify-center gap-2"
          >
            <LogOut size={16} />
            Logout
          </button>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex items-center justify-center min-h-screen p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-4xl mx-auto"
        >
          <div className="glass-card form-container text-center">
            {/* Welcome Section */}
            <div className="mb-8">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <Wallet className="text-white" size={32} />
              </div>
              <h1 className="text-3xl font-bold text-white mb-2">Welcome to E-Wallet</h1>
              <p className="text-white/70">Hello, {user?.name || 'User'}! Manage your digital wallet efficiently.</p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              {menuItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => navigate(item.path)}
                  className="glass-card p-4 hover:bg-white/15 transition-all duration-200 group"
                >
                  <item.icon className="text-white/80 group-hover:text-white mx-auto mb-2" size={24} />
                  <p className="text-white/80 group-hover:text-white text-sm font-medium">{item.name}</p>
                </button>
              ))}
            </div>

            {/* Action Message */}
            <p className="text-white/60 text-sm">
              Use the menu button in the top-left corner to navigate through different sections.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
