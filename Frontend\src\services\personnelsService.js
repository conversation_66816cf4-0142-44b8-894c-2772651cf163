import axios from 'axios';

const API_BASE_URL = 'http://localhost:8765';

export const getPersonnels = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/personnels`);
    return response.data;
  } catch (error) {
    return [];
  }
};


export const deletePersonnel = async (id) => {
  try {
    await axios.delete(`${API_BASE_URL}/personnels/${id}`);
  } catch (error) {
    // handle error
  }
};

export const createPersonnel = async (data) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/personnels`, data);
    return { success: true, personnel: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data?.message || 'Error' };
  }
};
