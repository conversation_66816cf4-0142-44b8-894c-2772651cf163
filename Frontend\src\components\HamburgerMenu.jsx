import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './HamburgerMenu.css';

const entities = [
  { name: 'Banques', path: '/banques' },
  { name: 'Comptes', path: '/comptes' },
  { name: 'Personnels', path: '/personnels' },
  { name: 'Users', path: '/users' },
];

const HamburgerMenu = ({ isOpen, onClose }) => (
  <nav className={`hamburger-menu glass-card ${isOpen ? 'open' : ''}`}> 
    <button className="close-btn glass-button" onClick={onClose}>&times;</button>
    <ul>
      {entities.map(entity => (
        <li key={entity.name}>
          <Link to={entity.path} className="glass-button menu-link">{entity.name}</Link>
        </li>
      ))}
    </ul>
  </nav>
);

export default HamburgerMenu;
