import React, { useState } from 'react';
import HamburgerMenu from './components/HamburgerMenu';
import { Outlet } from 'react-router-dom';
import './AppLayout.css';

const AppLayout = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <div className="app-layout">
      <button className="hamburger glass-button" onClick={() => setMenuOpen(true)}>
        <span className="hamburger-icon">&#9776;</span>
      </button>
      <HamburgerMenu isOpen={menuOpen} onClose={() => setMenuOpen(false)} />
      <main className="main-content glass-card">
        <Outlet />
      </main>
    </div>
  );
};

export default AppLayout;
