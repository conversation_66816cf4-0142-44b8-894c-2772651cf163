import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getUsers, deleteUser } from '../services/usersService';

const UsersList = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    const data = await getUsers();
    setUsers(Array.isArray(data) ? data : []);
    setLoading(false);
  };

  const handleDelete = async (id) => {
    await deleteUser(id);
    fetchUsers();
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
      <div className="list-header">
        <h2 className="text-2xl font-bold">Users</h2>
        <Link to="/users/add" className="glass-button">Add User</Link>
      </div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="entity-list">
          {users.map(user => (
            <div key={user.id} className="glass-card entity-item">
              <span>{user.name}</span>
              <div>
                <Link to={`/users/edit/${user.id}`} className="glass-button">Edit</Link>
                <button onClick={() => handleDelete(user.id)} className="glass-button">Delete</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default UsersList;
