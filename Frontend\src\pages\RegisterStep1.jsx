import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';

const RegisterStep1 = () => {
  const [formData, setFormData] = useState({ name: '', username: '', password: '' });
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    const result = await authService.register(formData);
    if (result.success) {
      // Pass user id or token to next step if needed
      navigate('/register/personnel', { state: { userId: result.user?.id } });
    } else {
      setError(result.error || 'Registration failed');
    }
  };

  return (
    <form className="glass-card form-container" onSubmit={handleSubmit}>
      <h2>Créer un compte</h2>
      <input className="glass-input" name="name" placeholder="Nom" value={formData.name} onChange={handleChange} required />
      <input className="glass-input" name="username" placeholder="Nom d'utilisateur" value={formData.username} onChange={handleChange} required />
      <input className="glass-input" name="password" type="password" placeholder="Mot de passe" value={formData.password} onChange={handleChange} required />
      {error && <div className="error-message">{error}</div>}
      <button className="glass-button" type="submit">Suivant</button>
    </form>
  );
};

export default RegisterStep1;
