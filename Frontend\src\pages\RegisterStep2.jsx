import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { personnelsService } from '../services/personnelsService';

const RegisterStep2 = () => {
  const [formData, setFormData] = useState({ nom: '', tel: '', mail: '', adresse: '', date_naissance: '' });
  const [error, setError] = useState('');
  const location = useLocation();
  const navigate = useNavigate();
  const userId = location.state?.userId;

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    if (!userId) {
      setError('User ID missing. Please register again.');
      return;
    }
    const result = await personnelsService.createPersonnel({ ...formData, user_id: userId });
    if (result.success) {
      navigate('/login');
    } else {
      setError(result.error || 'Failed to save personnel details');
    }
  };

  return (
    <form className="glass-card form-container" onSubmit={handleSubmit}>
      <h2>Informations du personnel</h2>
      <input className="glass-input" name="nom" placeholder="Nom" value={formData.nom} onChange={handleChange} required />
      <input className="glass-input" name="tel" placeholder="Téléphone" value={formData.tel} onChange={handleChange} />
      <input className="glass-input" name="mail" placeholder="Email" value={formData.mail} onChange={handleChange} />
      <input className="glass-input" name="adresse" placeholder="Adresse" value={formData.adresse} onChange={handleChange} />
      <input className="glass-input" name="date_naissance" type="date" value={formData.date_naissance} onChange={handleChange} />
      {error && <div className="error-message">{error}</div>}
      <button className="glass-button" type="submit">Terminer</button>
    </form>
  );
};

export default RegisterStep2;
